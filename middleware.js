import { NextResponse } from "next/server";

export function middleware(request) {
  const token = request.cookies.get("token");
  const { pathname } = request.nextUrl;

  // Redirect logged-in users away from login page
  if (token && pathname.startsWith("/auth")) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Routes that need authentication
  const protectedPaths = ["/dashboard", "/profile", "/settings"];

  // Redirect unauthenticated users to login with callbackUrl
  if (!token && protectedPaths.some((path) => pathname.startsWith(path))) {
    const loginUrl = new URL("/auth/login", request.url);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Make middleware run for protected routes and login page
export const config = {
  matcher: ["/auth/login", "/dashboard/:path*", "/auth/:path*"],
};
