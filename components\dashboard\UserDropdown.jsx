"use client";
import { User } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

const UserDropdown = () => {
  const [userDropdown, setUserDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const router = useRouter();
  const pathname = usePathname();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setUserDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // close on route change
  useEffect(() => {
    setUserDropdown(false);
  }, [pathname]);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className="flex items-center space-x-3 cursor-pointer rounded-lg p-2 transition-colors duration-200"
        onClick={() => setUserDropdown((prev) => !prev)}
      >
        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <div className="flex flex-col items-start">
          <p className="text-sm font-medium text-gray-700">John Doe</p>
          <p className="text-xs text-gray-500"><EMAIL></p>
        </div>
      </button>
      {userDropdown && (
        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg">
          <ul className="p-2">
            <li>
              <Link
                href="#"
                className="block px-4 py-2 hover:bg-orange-500 hover:text-white rounded-lg"
              >
                Settings
              </Link>
            </li>
            <li>
              <Link
                href="#"
                className="block px-4 py-2 hover:bg-orange-500 hover:text-white rounded-lg"
              >
                Change Password
              </Link>
            </li>
            <li>
              <Link
                href="#"
                className="block px-4 py-2 hover:bg-orange-500 hover:text-white rounded-lg"
              >
                Support Ticket
              </Link>
            </li>

            <li>
              <button
                href="#"
                className="block px-4 py-2 bg-red-100 hover:bg-red-500 text-red-700 hover:text-white w-full text-start rounded-lg"
              >
                Logout
              </button>
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default UserDropdown;
