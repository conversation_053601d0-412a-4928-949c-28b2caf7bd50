"use client";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import authImage from "../../assets/agent-auth-img.jpg";
import ApiPath from "../../network/api/api_path";
import NetworkService from "../../network/service/network_service";
import TokenService from "../../network/service/token_service";

const Login = () => {
  const network = new NetworkService();
  const tokenService = new TokenService();

  // states
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [emailVerificationData, setEmailVerificationData] = useState("");
  const [twoFaVerificationData, setTwoFaVerificationData] = useState("");

  //For routing
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

  // settings data for email verification
  const emailVerificationSettingData = async () => {
    try {
      const res = await network.globalGet(
        ApiPath.setting("email_verification")
      );
      if (res.status === "completed") {
        setEmailVerificationData(res.data.data);
      }
    } finally {
    }
  };

  // settings data for 2fa verification
  const twoFaVerificationSettingsData = async () => {
    try {
      const res = await network.globalGet(ApiPath.setting("fa_verification"));
      if (res.status === "completed") {
        setTwoFaVerificationData(res.data.data);
      }
    } finally {
    }
  };

  // handle login
  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    if (!email || !password) {
      toast.error("Please fill in all fields");
      return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      const requestBody = { email, password };
      const res = await network.globalPost(ApiPath.login, requestBody);
      if (res.status === "completed") {
        const token = res.data.data.token;
        network.tokenService.saveAccessToken(token);
        await userData(callbackUrl);
      }
    } finally {
    }
  };

  // after login get user data and do condition where to go
  const userData = async (redirectPath) => {
    try {
      const res = await network.get(ApiPath.agentProfile);
      if (res.status === "completed") {
        const emailVerifiedAt = res.data.data.user.email_verified_at;
        const email = res.data.data.user.email;
        const google2faSecret = res.data.data.user.google2fa_secret;
        const userTwoFa = res.data.data.user.two_fa;

        if (emailVerifiedAt === null && emailVerificationData === "1") {
          await emailVerification(email);
        } else if (
          userTwoFa === true &&
          google2faSecret === true &&
          twoFaVerificationData === "1"
        ) {
          router.push(
            `/auth/login/twoFa-verification?callbackUrl=${encodeURIComponent(
              redirectPath
            )}`
          );
        } else {
          toast.success("Login successful");
          router.push(redirectPath);
        }
      }
    } finally {
    }
  };

  // if email not verified and site email verification is on then send email verification
  const emailVerification = async (email) => {
    const requestBody = { email: email };
    const res = await network.post(ApiPath.emailVerify, requestBody);
    if (res.status === "completed") {
      toast.success("Email verification sent");
      router.push(
        `/auth/login/email-verification?callbackUrl=${encodeURIComponent(
          redirectPath
        )}`
      );
    }
  };

  useEffect(() => {
    emailVerificationSettingData();
    twoFaVerificationSettingsData();
  }, []);

  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <div className="w-full max-w-4xl bg-white shadow-lg rounded-lg p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="flex flex-col justify-center">
            <h1 className="text-3xl font-bold mb-5">Agent Login</h1>
            <form onSubmit={handleLoginSubmit}>
              {/* Email */}
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="border rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline border-gray-300"
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>

              {/* Password */}
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Password
                </label>
                <input
                  type="password"
                  className="border rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline border-gray-300"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>

              {/* Remember me + forgot */}
              <div className="mb-4 flex justify-between items-center">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-orange-500"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <span className="ml-2 text-gray-700">Remember Me</span>
                </label>
                <Link
                  href="/auth/login/forgot-password"
                  className="text-orange-500"
                >
                  Forgot Password?
                </Link>
              </div>

              <div className="flex justify-center">
                <button
                  type="submit"
                  className="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
                >
                  Login
                </button>
              </div>

              <div className="mt-4 text-center">
                <p className="text-gray-700">
                  Don&apos;t have an account?{" "}
                  <Link href="/auth/register" className="text-orange-500">
                    Register
                  </Link>
                </p>
              </div>
            </form>
          </div>
          <div className="flex items-center justify-center">
            <Image src={authImage} alt="Auth Image" className="w-full h-auto" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
