import { Bell, User } from "lucide-react";
import UserDropdown from "./UserDropdown";

const DashboardHeader = () => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 h-16">
      <div className="flex items-center justify-between h-full px-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Welcome back</h1>
        </div>

        <div className="flex items-center space-x-4">
          <button className="relative p-2 text-gray-400 hover:text-orange-500 hover:bg-gray-100 rounded-full transition-colors duration-200">
            <Bell className="w-6 h-6" />
            <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>

          <UserDropdown />
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
