"use client";
import {
  BarChart3,
  Calendar,
  FileText,
  Home,
  Settings,
  Users,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const DashboardSidebar = () => {
  const currentPath = usePathname();

  return (
    <div className="w-64 bg-white shadow-lg flex flex-col">
      {/* Logo */}
      <div className="flex items-center h-16 px-6 bg-orange-500">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
            <span className="text-orange-500 font-bold text-lg">M</span>
          </div>
          <span className="ml-3 text-white font-semibold text-lg">
            MoneyChain
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 mt-8 px-4">
        <ul className="space-y-2">
          {/* Dashboard */}
          <li>
            <Link
              href="/dashboard"
              className={`group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                currentPath === "/dashboard"
                  ? "bg-orange-50 text-orange-600 border-r-4 border-orange-500"
                  : "text-gray-600 hover:bg-gray-50 hover:text-orange-600"
              }`}
            >
              <Home
                className={`mr-3 h-5 w-5 ${
                  currentPath === "/dashboard"
                    ? "text-orange-500"
                    : "text-gray-400 group-hover:text-orange-500"
                }`}
              />
              Dashboard
            </Link>
          </li>
          {/* Settings */}
          <li>
            <Link
              href="/dashboard/settings"
              className={`group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                currentPath === "/dashboard/settings"
                  ? "bg-orange-50 text-orange-600 border-r-4 border-orange-500"
                  : "text-gray-600 hover:bg-gray-50 hover:text-orange-600"
              }`}
            >
              <Settings
                className={`mr-3 h-5 w-5 ${
                  currentPath === "/dashboard/settings"
                    ? "text-orange-500"
                    : "text-gray-400 group-hover:text-orange-500"
                }`}
              />
              Settings
            </Link>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default DashboardSidebar;
